"use client"

import { useEffect, useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { useAppDispatch, useAppSelector } from "@/store"
import { nextStep, prevStep, setDateTime } from "@/store/appointmentBookingSlice"
import { fetchSlots, fetchMoreSlots, navigateMonth, clearSlots } from "@/store/slotsSlice"

interface CalendarDay {
  date: number;
  isCurrentMonth: boolean;
  fullDate: Date;
  isPast: boolean;
  hasAvailableSlots?: boolean;
  daySlot?: {
    slots: {
      available: boolean;
      time: string;
    }[];
  };
}

const monthNames = [
  "Januar",
  "Februar",
  "März",
  "April",
  "Mai",
  "Juni",
  "Juli",
  "August",
  "September",
  "Oktober",
  "November",
  "Dezember",
]

const dayNames = ["MO", "DI", "MI", "DO", "FR", "SA", "SO"]

export function DateTimeSelection() {
  const dispatch = useAppDispatch()
  const { months, loading, currentMonthIndex, hasMoreNext, hasMorePrev, error, lastServiceIds } = useAppSelector((state) => state.slots)
  const { date: selectedDate, time: selectedTime, services } = useAppSelector((state) => state.appointmentBooking.appointmentData)
  const [showTimeSelection, setShowTimeSelection] = useState(false)
  const [selectedDateSlots, setSelectedDateSlots] = useState<string[]>([])

  // Memoize service IDs to prevent unnecessary re-renders
  const currentServiceIds = useMemo(() => {
    return services.map(service => parseInt(service.id)).sort()
  }, [services])

  useEffect(() => {
    // Only proceed if we have selected services
    if (services.length === 0) return

    const cachedServiceIds = lastServiceIds.slice().sort()

    // Check if service selection has changed
    const serviceSelectionChanged = JSON.stringify(currentServiceIds) !== JSON.stringify(cachedServiceIds)

    // Fetch slots if:
    // 1. We don't have any cached slots (months.length === 0), OR
    // 2. Service selection has changed
    // AND we're not currently loading
    if ((months.length === 0 || serviceSelectionChanged) && !loading) {
      // Clear existing slots if service selection changed
      if (serviceSelectionChanged && months.length > 0) {
        dispatch(clearSlots())
      }

      const today = new Date().toISOString().split("T")[0]
      dispatch(fetchSlots({ startDate: today, serviceIds: currentServiceIds }))
    }
  }, [dispatch, months.length, currentServiceIds, lastServiceIds, loading, services.length])

  const currentMonth = months[currentMonthIndex]
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  const getDaysInMonth = () => {
    if (!currentMonth) return []

    const year = currentMonth.year
    const month = currentMonth.month - 1 // JavaScript months are 0-indexed
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = (firstDay.getDay() + 6) % 7 // Convert to Monday = 0

    const days = []

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      const prevMonthDay = new Date(year, month, 1 - startingDayOfWeek + i)
      days.push({ date: prevMonthDay.getDate(), isCurrentMonth: false, fullDate: prevMonthDay, isPast: true })
    }

    // Add days of the current month
    for (let day = 1; day <= daysInMonth; day++) {
      const fullDate = new Date(year, month, day)
      const isPast = fullDate < today
      const dateString = fullDate.toISOString().split("T")[0]
      const daySlot = currentMonth.days.find((d) => d.date === dateString)
      const hasAvailableSlots = daySlot?.slots.some((slot) => slot.available) || false

      days.push({
        date: day,
        isCurrentMonth: true,
        fullDate,
        isPast,
        hasAvailableSlots,
        daySlot,
      })
    }

    return days
  }

  const selectDate = (day: CalendarDay) => {
    if (!day.isCurrentMonth || day.isPast || !day.hasAvailableSlots) return

    const dateString = day.fullDate.toISOString().split("T")[0]
    dispatch(setDateTime({ date: dateString, time: selectedTime }))
    setSelectedDateSlots(day.daySlot?.slots.filter((slot) => slot.available).map((slot) => slot.time) || [])
    setShowTimeSelection(true)
  }

  const selectTime = (time: string) => {
    dispatch(setDateTime({ date: selectedDate, time }))
  }

  const handleNext = () => {
    if (selectedDate && selectedTime) {
      dispatch(nextStep())
    }
  }

  const handleBack = () => {
    if (showTimeSelection) {
      setShowTimeSelection(false)
    } else {
      dispatch(prevStep())
    }
  }

  const handleMonthNavigation = async (direction: "prev" | "next") => {
    const serviceIds = services.map(service => parseInt(service.id))

    if (direction === "prev") {
      if (currentMonthIndex > 0) {
        // Navigate to previous month (already loaded)
        dispatch(navigateMonth("prev"))
      } else if (hasMorePrev) {
        // Fetch more previous months
        const firstMonth = months[0]
        const referenceDate = new Date(firstMonth.year, firstMonth.month - 4, 1).toISOString().split("T")[0]
        await dispatch(fetchMoreSlots({ direction: "prev", referenceDate, serviceIds }))
      }
    } else {
      if (currentMonthIndex < months.length - 1) {
        // Navigate to next month (already loaded)
        dispatch(navigateMonth("next"))
      } else if (hasMoreNext) {
        // Fetch more next months
        const lastMonth = months[months.length - 1]
        const referenceDate = new Date(lastMonth.year, lastMonth.month, 1).toISOString().split("T")[0]
        await dispatch(fetchMoreSlots({ direction: "next", referenceDate, serviceIds }))
        // After fetching, navigate to the next month
        dispatch(navigateMonth("next"))
      }
    }
  }

  if (showTimeSelection) {
    return (
      <div className="w-full max-w-4xl mx-auto h-full">
        <Card className="w-full h-full flex flex-col">
          <CardHeader>
            <CardTitle>Wählen Sie ein Datum und eine Uhrzeit</CardTitle>
          </CardHeader>
          <CardContent className="p-6 flex-1 flex flex-col">
            <div className="flex justify-center items-center gap-4 flex-wrap">
              {selectedDateSlots.map((time) => (
                <Button
                  key={time}
                  variant={selectedTime === time ? "default" : "outline"}
                  className="h-12 text-lg"
                  onClick={() => selectTime(time)}
                >
                  {time}
                </Button>
              ))}
            </div>

            <div className="flex justify-between">
              <Button variant="outline" onClick={handleBack}>
                Zurück
              </Button>
              <Button onClick={handleNext} disabled={!selectedTime} className="bg-green-600 hover:bg-green-700">
                Weiter
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const days = getDaysInMonth()
  const canGoPrev = currentMonthIndex > 0 || hasMorePrev
  const canGoNext = currentMonthIndex < months.length - 1 || hasMoreNext

  // Show service requirement message if no services are selected
  if (services.length === 0) {
    return (
      <div className="w-full max-w-4xl mx-auto h-full">
        <Card className="w-full h-full flex flex-col">
          <CardHeader>
            <CardTitle>Wählen Sie ein Datum und eine Uhrzeit</CardTitle>
          </CardHeader>
          <CardContent className="p-6 flex-1 flex flex-col">
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <p className="text-lg text-gray-600 mb-4">
                  Bitte wählen Sie zuerst einen Service aus, um verfügbare Termine zu sehen.
                </p>
                <Button onClick={() => dispatch(prevStep())} variant="outline">
                  Zurück zur Service-Auswahl
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Show error message if slot fetching failed
  if (error) {
    return (
      <div className="w-full max-w-4xl mx-auto h-full">
        <Card className="w-full h-full flex flex-col">
          <CardHeader>
            <CardTitle>Wählen Sie ein Datum und eine Uhrzeit</CardTitle>
          </CardHeader>
          <CardContent className="p-6 flex-1 flex flex-col">
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <p className="text-lg text-red-600 mb-4">
                  Fehler beim Laden der verfügbaren Termine: {error}
                </p>
                <div className="space-x-2">
                  <Button
                    onClick={() => {
                      const today = new Date().toISOString().split("T")[0]
                      const serviceIds = services.map(service => parseInt(service.id))
                      dispatch(fetchSlots({ startDate: today, serviceIds }))
                    }}
                    variant="default"
                  >
                    Erneut versuchen
                  </Button>
                  <Button onClick={() => dispatch(prevStep())} variant="outline">
                    Zurück
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="w-full max-w-4xl mx-auto h-full">
      <Card className="w-full h-full flex flex-col">
        <CardHeader>
          <CardTitle>Wählen Sie ein Datum und eine Uhrzeit</CardTitle>
        </CardHeader>
        <CardContent className="p-6 flex-1 flex flex-col">
          {loading ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <div className="text-lg text-gray-600 mb-2">Lade verfügbare Termine...</div>
                {/* <div className="text-sm text-gray-500">
                  Services: {services.map(s => s.name).join(', ')}
                </div> */}
              </div>
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between mb-6">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleMonthNavigation("prev")}
                  disabled={!canGoPrev || loading}
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <h2 className="text-xl font-semibold">
                  {currentMonth && `${monthNames[currentMonth.month - 1]} ${currentMonth.year}`}
                </h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleMonthNavigation("next")}
                  disabled={!canGoNext || loading}
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>

              <div className="grid grid-cols-7 gap-2 mb-4">
                {dayNames.map((day) => (
                  <div key={day} className="text-center text-sm font-medium text-gray-500 py-2">
                    {day}
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-7 gap-2 mb-8 flex-1">
                {days.map((day, index) => (
                  <button
                    key={day.fullDate ? day.fullDate.toISOString().split("T")[0] : `empty-${index}`}
                    onClick={() => selectDate(day)}
                    disabled={!day.isCurrentMonth || day.isPast || !day.hasAvailableSlots}
                    className={`
                      h-12 rounded-lg text-sm font-medium transition-colors
                      ${
                        !day.isCurrentMonth || day.isPast || !day.hasAvailableSlots
                          ? "text-gray-300 cursor-not-allowed bg-gray-50"
                          : "text-gray-700 hover:bg-gray-100 cursor-pointer bg-gray-100"
                      }
                      ${
                        selectedDate === day.fullDate?.toISOString().split("T")[0]
                          ? "bg-gray-900 text-white hover:bg-gray-800"
                          : ""
                      }
                    `}
                  >
                    {day.date}
                  </button>
                ))}
              </div>

              <div className="flex justify-between">
                <Button variant="outline" onClick={handleBack}>
                  Zurück
                </Button>
                <Button
                  onClick={() => setShowTimeSelection(true)}
                  disabled={!selectedDate}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Weiter
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
