import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { nextCookies } from "better-auth/next-js";
import { magicLink } from "better-auth/plugins";
import { getDB } from "@database/index";
import { getEnv } from "@utils/ctx";
import { authSchema } from "@/database/schema";
import { emailService } from "@/lib/email";

/**
 * Initializes Better Auth with Drizzle ORM and Next.js integration.
 * Uses OpenNext getEnv for all secrets/config.
 *
 * Required env vars:
 *   - BETTER_AUTH_SECRET
 *   - BETTER_AUTH_URL
 *   - (optional) GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, etc.
 *
 * @see https://github.com/better-auth/better-auth
 */
export const auth = async () => {
    const env = await getEnv();
    const db = await getDB();
    return betterAuth({
        database: drizzleAdapter(db, {
            provider: "pg",
            usePlural: true,
            schema: authSchema,
        }),
        secret: env.BETTER_AUTH_SECRET,
        baseUrl: env.ENV === "production" ? env.PROD_BETTER_AUTH :  env.BETTER_AUTH_URL,
        user: {
            additionalFields: {
                role: {
                    type: "string",
                    required: false,
                    defaultValue: "user",
                    input: false, // Don't allow user to set role during registration
                },
            },
        },
        emailAndPassword: {
            enabled: true,
        },
        plugins: [
            nextCookies(),
            magicLink({
                sendMagicLink: async ({ email, url }) => {
                    try {
                        const result = await emailService.sendMagicLink(email, url);
                        if (!result.success) {
                            console.error("Email service failed:", result.error);
                            throw new Error(`Failed to send magic link email: ${result.error}`);
                        }
                    } catch (error) {
                        console.error("Exception in magic link handler:", error);
                        throw error;
                    }
                }
            })
        ],
    });
};

export const getAuth = async () => await auth();

// Export the auth instance type for client-side type inference
export type AuthInstance = Awaited<ReturnType<typeof auth>>;