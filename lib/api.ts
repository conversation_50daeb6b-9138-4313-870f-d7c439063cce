import type { AppointmentData } from "../store/appointmentBookingSlice"

export const bookAppointment = async (appointmentData: AppointmentData) => {
  // Transform the frontend payload to match the API's expected format
  const apiPayload = {
    services: appointmentData.services.map(service => parseInt(service.id)),
    slot: `${appointmentData.date}T${appointmentData.time}:00.000Z`,
    firstName: appointmentData.contact.firstName,
    lastName: appointmentData.contact.lastName,
    email: appointmentData.contact.email,
    phone: appointmentData.contact.phone,
    message: appointmentData.contact.additionalInfo || undefined,
  }

  const response = await fetch("/api/booking/book", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(apiPayload),
  })

  if (!response.ok) {
    let errorMessage = "Failed to book appointment"

    try {
      const errorData = await response.json() as { message?: string; error?: string }
      // Use the server's error message if available
      if (errorData.message) {
        errorMessage = errorData.message
      } else if (errorData.error) {
        errorMessage = errorData.error
      }
    } catch {
      // If we can't parse the error response, use the status text
      errorMessage = response.statusText || errorMessage
    }

    throw new Error(errorMessage)
  }

  return response.json()
}
