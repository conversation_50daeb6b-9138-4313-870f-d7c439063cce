import { createSlice, type PayloadAction } from "@reduxjs/toolkit"

export type Service = {
  id: string
  name: string
  displayName?: string
  externalName: string
  duration: number
  price: string
  gross: number
  groupNames?: string[]
  productGroup?: string
  selected?: boolean
  image?: string
}

export type AppointmentData = {
  services: Service[]
  date: string
  time: string
  contact: {
    firstName: string
    lastName: string
    phone: string
    email: string
    additionalInfo: string
  }
}

interface AppointmentBookingState {
  currentStep: number
  appointmentData: AppointmentData
}

const initialState: AppointmentBookingState = {
  currentStep: 1,
  appointmentData: {
    services: [],
    date: "",
    time: "",
    contact: {
      firstName: "",
      lastName: "",
      phone: "",
      email: "",
      additionalInfo: "",
    },
  },
}

const appointmentBookingSlice = createSlice({
  name: "appointmentBooking",
  initialState,
  reducers: {
    setCurrentStep: (state, action: PayloadAction<number>) => {
      state.currentStep = action.payload
    },
    nextStep: (state) => {
      state.currentStep += 1
    },
    prevStep: (state) => {
      state.currentStep -= 1
    },
    updateAppointmentData: (state, action: PayloadAction<Partial<AppointmentData>>) => {
      state.appointmentData = { ...state.appointmentData, ...action.payload }
    },
    setServices: (state, action: PayloadAction<Service[]>) => {
      state.appointmentData.services = action.payload
    },
    setDateTime: (state, action: PayloadAction<{ date: string; time: string }>) => {
      state.appointmentData.date = action.payload.date
      state.appointmentData.time = action.payload.time
    },
    setContact: (state, action: PayloadAction<AppointmentData["contact"]>) => {
      state.appointmentData.contact = action.payload
    },
    resetAppointment: (state) => {
      state.currentStep = 1
      state.appointmentData = initialState.appointmentData
    },
  },
})

export const {
  setCurrentStep,
  nextStep,
  prevStep,
  updateAppointmentData,
  setServices,
  setDateTime,
  setContact,
  resetAppointment,
} = appointmentBookingSlice.actions

export { appointmentBookingSlice };
export default appointmentBookingSlice.reducer
